<template>
  <div class="coupon-code-section">
    <div class="row items-center q-mb-xs">
      <q-icon name="local_offer" color="primary" size="xs" class="q-mr-xs" />
      <div class="text-subtitle2 text-weight-medium">优惠券</div>
    </div>
    
    <q-card flat bordered class="q-pa-sm">
      <!-- 优惠码输入区域 -->
      <div class="row q-col-gutter-sm items-end">
        <div class="col">
          <q-input
            v-model="couponCode"
            label="输入优惠码"
            outlined
            dense
            placeholder="请输入优惠码"
            :error="!!errorMessage"
            :error-message="errorMessage"
            :loading="verifying"
            @keyup.enter="verifyCoupon"
            @input="clearError"
          >
            <template #prepend>
              <q-icon name="confirmation_number" size="xs" />
            </template>
            <template #append>
              <q-btn
                v-if="couponCode && !validCoupon"
                flat
                dense
                color="primary"
                label="验证"
                :loading="verifying"
                @click="verifyCoupon"
                size="sm"
              />
              <q-btn
                v-if="validCoupon"
                flat
                dense
                color="negative"
                icon="clear"
                @click="clearCoupon"
                size="sm"
              />
            </template>
          </q-input>
        </div>
      </div>
      
      <!-- 验证成功的优惠券信息 -->
      <div v-if="validCoupon" class="q-mt-sm">
        <q-card flat class="bg-positive-1 q-pa-sm">
          <div class="row items-center justify-between">
            <div class="col">
              <div class="text-body2 text-weight-medium text-positive">
                <q-icon name="check_circle" class="q-mr-xs" />
                {{ validCoupon.couponName }}
              </div>
              <div class="text-caption text-grey-8">
                优惠金额: 
                <span class="text-negative text-weight-medium">
                  <span v-if="validCoupon.discountType === 1">-¥{{ fen2yuan(validCoupon.discountAmount) }}</span>
                  <span v-else-if="validCoupon.discountType === 2">{{ validCoupon.discountPercent }}折</span>
                </span>
              </div>
            </div>
          </div>
        </q-card>
      </div>
      
      <!-- 提示信息 -->
      <div v-if="!validCoupon && !errorMessage" class="text-caption text-grey-6 q-mt-xs">
        输入优惠码享受更多优惠
      </div>
    </q-card>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { useQuasar } from 'quasar';
import CouponApi from '../../composables/couponApi';
import { fen2yuan } from '../../utils/utils';

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  validatedCoupon: {
    type: Object,
    default: null,
  },
});

const emit = defineEmits(['update:modelValue', 'coupon-validated', 'coupon-cleared']);

const $q = useQuasar();

const couponCode = ref(props.modelValue);
const validCoupon = ref(props.validatedCoupon);
const verifying = ref(false);
const errorMessage = ref('');

// 验证优惠码
async function verifyCoupon() {
  if (!couponCode.value.trim()) {
    errorMessage.value = '请输入优惠码';
    return;
  }
  
  try {
    verifying.value = true;
    errorMessage.value = '';
    
    const { code, data, msg } = await CouponApi.verifyCouponCode(couponCode.value.trim());
    
    if (code === 0 && data) {
      if (data.valid) {
        validCoupon.value = data;
        emit('update:modelValue', couponCode.value.trim());
        emit('coupon-validated', data);
        
        $q.notify({
          color: 'positive',
          message: '优惠码验证成功',
          icon: 'check_circle',
        });
      } else {
        errorMessage.value = data.invalidReason || '优惠码无效';
        validCoupon.value = null;
      }
    } else {
      errorMessage.value = msg || '验证失败，请重试';
      validCoupon.value = null;
    }
  } catch (error) {
    console.error('验证优惠码失败:', error);
    errorMessage.value = '验证失败，请重试';
    validCoupon.value = null;
  } finally {
    verifying.value = false;
  }
}

// 清除优惠码
function clearCoupon() {
  couponCode.value = '';
  validCoupon.value = null;
  errorMessage.value = '';
  emit('update:modelValue', '');
  emit('coupon-cleared');
}

// 清除错误信息
function clearError() {
  if (errorMessage.value) {
    errorMessage.value = '';
  }
  // 如果用户修改了优惠码，清除之前的验证结果
  if (validCoupon.value && couponCode.value !== props.modelValue) {
    validCoupon.value = null;
    emit('coupon-cleared');
  }
}

// 监听外部传入的值变化
watch(() => props.modelValue, (newValue) => {
  couponCode.value = newValue;
});

watch(() => props.validatedCoupon, (newValue) => {
  validCoupon.value = newValue;
});

// 监听couponCode变化，同步到父组件
watch(couponCode, (newValue) => {
  emit('update:modelValue', newValue);
});
</script>

<style lang="scss" scoped>
.coupon-code-section {
  .q-input {
    :deep(.q-field__control) {
      height: 40px;
    }
  }
  
  .q-card {
    border-radius: 4px;
    transition: all 0.2s ease;
    
    &:hover {
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
