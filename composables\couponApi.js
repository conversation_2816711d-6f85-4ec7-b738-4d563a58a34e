const CouponApi = {
  // 获得优惠劵模板列表
  getCouponTemplateListByIds: (ids) => {
    return useClientGet('/promotion/coupon-template/list-by-ids', {
      params: { ids },
      custom: {
        showLoading: false, // 不展示 Loading，避免领取优惠劵时，不成功提示
        showError: false,
      },
    });
  },
  // 获得优惠劵模版列表
  getCouponTemplateList: (spuId, productScope, count) => {
    return useClientGet('/promotion/coupon-template/list', {
      params: { spuId, productScope, count },
    });
  },
  // 获得优惠劵模版分页
  getCouponTemplatePage: (params) => {
    return useClientGet('/promotion/coupon-template/page', {
      params,
    });
  },
  // 获得优惠劵模版
  getCouponTemplate: (id) => {
    return useClientGet('/promotion/coupon-template/get', {
      params: { id },
    });
  },
  // 我的优惠劵列表
  getCouponPage: (params) => {
    return useClientGet('/promotion/coupon/page', {
      params,
    });
  },
  // 领取优惠券
  takeCoupon: (templateId) => {
    return useClientPost('/promotion/coupon/take', {
      data: { templateId },
      custom: {
        auth: true,
        showLoading: true,
        loadingMsg: '领取中',
        showSuccess: true,
        successMsg: '领取成功',
      },
    });
  },
  // 获得优惠劵
  getCoupon: (id) => {
    return useClientGet('/promotion/coupon/get', {
      params: { id },
    });
  },
  // 获得未使用的优惠劵数量
  getUnusedCouponCount: () => {
    return useClientGet('/promotion/coupon/get-unused-count', {
      custom: {
        showLoading: false,
        auth: true,
      },
    });
  },

  // 验证优惠码
  verifyCouponCode: (code) => {
    return useClientPost('/promotion/coupon//validate-code', {
      params: { code },
      custom: {
        showLoading: false,
        auth: true,
      },
    });
  },
};

export default CouponApi;
