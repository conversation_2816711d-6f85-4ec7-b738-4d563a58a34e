<template>
  <div class="compact-services-section">
    <!-- 保险服务 -->
    <div v-if="insuranceServices && insuranceServices.length > 0" class="service-group q-mb-sm">
      <div class="row items-center q-mb-xs">
        <q-icon name="security" color="primary" size="xs" class="q-mr-xs" />
        <div class="text-subtitle2 text-weight-medium">保险服务</div>
      </div>
      <q-card flat bordered class="q-pa-sm">
        <div class="row q-col-gutter-xs">
          <div 
            v-for="service in insuranceServices" 
            :key="service.id"
            class="col-12 col-sm-6 col-md-4"
          >
            <q-card 
              flat 
              bordered 
              :class="{ 'selected-service': selectedInsurance.includes(service.id) }" 
              class="service-item cursor-pointer"
              @click="toggleInsurance(service.id)"
            >
              <q-card-section class="q-pa-xs">
                <div class="row items-center no-wrap">
                  <q-checkbox 
                    v-model="selectedInsurance" 
                    :val="service.id" 
                    color="primary" 
                    size="xs"
                    class="q-mr-xs" 
                    @click.stop 
                  />
                  <div class="col text-caption">
                    <div class="text-weight-medium">{{ service.name }}</div>
                    <div class="text-primary">¥{{ fen2yuan(service.price) }}</div>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </q-card>
    </div>

    <!-- 免费服务 -->
    <div v-if="freeServices && freeServices.length > 0" class="service-group q-mb-sm">
      <div class="row items-center q-mb-xs">
        <q-icon name="card_giftcard" color="primary" size="xs" class="q-mr-xs" />
        <div class="text-subtitle2 text-weight-medium">免费服务</div>
      </div>
      <q-card flat bordered class="q-pa-sm">
        <div class="row q-col-gutter-xs">
          <div 
            v-for="service in freeServices" 
            :key="service.id"
            class="col-12 col-sm-6 col-md-4"
          >
            <q-card 
              flat 
              bordered 
              :class="{ 'selected-service': selectedFree.includes(service.id) }" 
              class="service-item cursor-pointer"
              @click="toggleFree(service.id)"
            >
              <q-card-section class="q-pa-xs">
                <div class="row items-center no-wrap">
                  <q-checkbox 
                    v-model="selectedFree" 
                    :val="service.id" 
                    color="primary" 
                    size="xs"
                    class="q-mr-xs" 
                    @click.stop 
                  />
                  <div class="col text-caption">
                    <div class="text-weight-medium">{{ service.name }}</div>
                    <div class="text-positive">免费</div>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </q-card>
    </div>

    <!-- 增值服务 -->
    <div v-if="chargeServices && chargeServices.length > 0" class="service-group q-mb-sm">
      <div class="row items-center q-mb-xs">
        <q-icon name="add_circle" color="primary" size="xs" class="q-mr-xs" />
        <div class="text-subtitle2 text-weight-medium">增值服务</div>
      </div>
      <q-card flat bordered class="q-pa-sm">
        <div class="row q-col-gutter-xs">
          <div 
            v-for="service in chargeServices" 
            :key="service.id"
            class="col-12 col-sm-6 col-md-4"
          >
            <q-card 
              flat 
              bordered 
              :class="{ 'selected-service': selectedCharge.includes(service.id) }" 
              class="service-item cursor-pointer"
              @click="toggleCharge(service.id)"
            >
              <q-card-section class="q-pa-xs">
                <div class="row items-center no-wrap">
                  <q-checkbox 
                    v-model="selectedCharge" 
                    :val="service.id" 
                    color="primary" 
                    size="xs"
                    class="q-mr-xs" 
                    @click.stop 
                  />
                  <div class="col text-caption">
                    <div class="text-weight-medium">{{ service.name }}</div>
                    <div class="text-primary">¥{{ fen2yuan(service.price) }}</div>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </q-card>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import { fen2yuan } from '../../utils/utils';

const props = defineProps({
  insuranceServices: {
    type: Array,
    default: () => [],
  },
  freeServices: {
    type: Array,
    default: () => [],
  },
  chargeServices: {
    type: Array,
    default: () => [],
  },
  selectedInsuranceServices: {
    type: Array,
    default: () => [],
  },
  selectedFreeServices: {
    type: Array,
    default: () => [],
  },
  selectedChargeServices: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['update:selectedInsuranceServices', 'update:selectedFreeServices', 'update:selectedChargeServices']);

const selectedInsurance = ref([...props.selectedInsuranceServices]);
const selectedFree = ref([...props.selectedFreeServices]);
const selectedCharge = ref([...props.selectedChargeServices]);

function toggleInsurance(serviceId) {
  const index = selectedInsurance.value.indexOf(serviceId);
  if (index > -1) {
    selectedInsurance.value.splice(index, 1);
  } else {
    selectedInsurance.value.push(serviceId);
  }
  emit('update:selectedInsuranceServices', [...selectedInsurance.value]);
}

function toggleFree(serviceId) {
  const index = selectedFree.value.indexOf(serviceId);
  if (index > -1) {
    selectedFree.value.splice(index, 1);
  } else {
    selectedFree.value.push(serviceId);
  }
  emit('update:selectedFreeServices', [...selectedFree.value]);
}

function toggleCharge(serviceId) {
  const index = selectedCharge.value.indexOf(serviceId);
  if (index > -1) {
    selectedCharge.value.splice(index, 1);
  } else {
    selectedCharge.value.push(serviceId);
  }
  emit('update:selectedChargeServices', [...selectedCharge.value]);
}

// 监听props变化
watch(() => props.selectedInsuranceServices, (newVal) => {
  selectedInsurance.value = [...newVal];
});

watch(() => props.selectedFreeServices, (newVal) => {
  selectedFree.value = [...newVal];
});

watch(() => props.selectedChargeServices, (newVal) => {
  selectedCharge.value = [...newVal];
});
</script>

<style lang="scss" scoped>
.compact-services-section {
  .service-item {
    border-radius: 4px;
    transition: all 0.2s ease;
    min-height: 50px;
    
    &:hover {
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    &.selected-service {
      border-color: #1976d2;
      background-color: rgba(25, 118, 210, 0.05);
    }
  }
  
  .service-group {
    .text-subtitle2 {
      font-size: 0.8rem;
    }
  }
}
</style>
